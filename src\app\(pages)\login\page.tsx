'use client';

import LoginForm from '@/components/auth/LoginForm';
import { useAuth } from '@/lib/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Head from 'next/head';

export default function LoginPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    // If user is authenticated, redirect to profile page
    if (user) {
      setIsRedirecting(true);
      router.push('/profile');
    }
  }, [user, router]);

  // Show loading state while auth is being checked or during redirect
  if (authLoading || isRedirecting) {
    return (
      <div className="min-h-screen py-12 bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-pulse text-center">
          <div className="h-8 w-32 bg-gray-300 dark:bg-gray-700 rounded mx-auto mb-4"></div>
          <div className="h-4 w-48 bg-gray-200 dark:bg-gray-800 rounded mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Sign In - Hiel Tech</title>
        <meta name="description" content="Sign in to your Hiel Tech account" />
        <meta name="robots" content="noindex" />
      </Head>
      <div className="min-h-screen py-12 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <LoginForm />
        </div>
      </div>
    </>
  );
}