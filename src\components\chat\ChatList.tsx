'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import { db, ChatRoom, supabase } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';

interface ChatListProps {
  onSelectRoom: (room: ChatRoom) => void;
  selectedRoomId?: string;
}

export default function ChatList({ onSelectRoom, selectedRoomId }: ChatListProps) {
  const { user, isAdmin } = useAuth();
  const [rooms, setRooms] = useState<ChatRoom[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const channelRef = useRef<any>(null);
  const isSubscribedRef = useRef(false);
  const mountedRef = useRef(true);
  const loadTimeoutRef = useRef<NodeJS.Timeout>();

  // Debounced room loading to prevent excessive API calls
  const loadRooms = useCallback(async (debounceMs?: number) => {
    if (!user?.id || !mountedRef.current) return;

    // Clear any existing timeout
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
    }

    // Debounce the loading
    loadTimeoutRef.current = setTimeout(async () => {
      try {
        setLoading(true);
        console.log('Loading chat rooms for user:', user.id);
        const chatRooms = await db.getChatRooms(user.id);
        
        if (mountedRef.current) {
          setRooms(chatRooms);
          setError('');
        }
      } catch (error) {
        console.error('Error loading chat rooms:', error);
        if (mountedRef.current) {
          setError('Failed to load chat rooms');
        }
      } finally {
        if (mountedRef.current) {
          setLoading(false);
        }
      }
    }, debounceMs || 0);
  }, [user?.id]);

  // Load chat rooms on mount
  useEffect(() => {
    loadRooms(0);
  }, [loadRooms]);

  // Set up real-time subscriptions with better management
  useEffect(() => {
    if (!user?.id || !mountedRef.current) return;

    // Prevent multiple subscriptions
    if (isSubscribedRef.current) {
      console.log('Already subscribed, skipping subscription setup');
      return;
    }

    // Clean up any existing channel first
    if (channelRef.current) {
      console.log('Cleaning up existing chat list channel');
      try {
        channelRef.current.unsubscribe();
      } catch (error) {
        console.warn('Error cleaning up existing channel:', error);
      }
      channelRef.current = null;
    }

    console.log('Setting up chat rooms realtime subscriptions');

    // Use timestamp to ensure unique channel names
    const timestamp = Date.now();
    const channelName = `chat_list_${user.id}_${timestamp}`;

    const channel = supabase
      .channel(channelName, {
        config: {
          broadcast: { self: false },
          presence: { key: user.id },
        },
      })
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chat_rooms',
        },
        (payload) => {
          console.log('Chat rooms updated:', payload);
          // Debounced reload to prevent excessive API calls
          loadRooms(500);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
        },
        (payload) => {
          console.log('New chat message:', payload);
          // Reload room list to update last message
          if (payload.new && user?.id && mountedRef.current) {
            loadRooms(300);
          }
        }
      )
      .subscribe((status, err) => {
        console.log('Chat rooms subscription status:', status, err);
        
        if (!mountedRef.current) return;

        if (status === 'SUBSCRIBED') {
          setError(''); // Clear any previous errors
          isSubscribedRef.current = true;
          console.log('Successfully subscribed to chat list channel');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Chat subscription error:', err);
          isSubscribedRef.current = false;
          setError('Connection issues with chat. Some features may not work properly.');
        } else if (status === 'TIMED_OUT') {
          console.error('Chat subscription timed out:', err);
          isSubscribedRef.current = false;
          setError('Chat connection timed out. Please refresh the page.');
        } else if (status === 'CLOSED') {
          console.log('Chat subscription closed');
          isSubscribedRef.current = false;
        }
      });

    channelRef.current = channel;

    return () => {
      console.log('Cleaning up chat rooms subscriptions');
      isSubscribedRef.current = false;
      
      if (channelRef.current) {
        try {
          channelRef.current.unsubscribe().then(() => {
            console.log('Successfully unsubscribed from chat list channel');
          }).catch((error: unknown) => {
            console.error('Error unsubscribing from chat list channel:', error);
          });
        } catch (error) {
          console.warn('Error during channel cleanup:', error);
        }
        channelRef.current = null;
      }

      // Clear any pending timeouts
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current);
      }
    };
  }, [user?.id, loadRooms]); // Stable dependencies

  // Component cleanup
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const getRoomDisplayName = (room: ChatRoom) => {
    if (room.name) return room.name;
    
    if (room.type === 'applicant_admin') {
      // For applicant-admin chats, show the other participant's name
      const otherParticipant = room.participants?.find(p => p.user_id !== user?.id);
      return otherParticipant?.user?.display_name || otherParticipant?.user?.email || 'Application Chat';
    }
    
    return 'Admin Chat';
  };

  const getRoomSubtitle = (room: ChatRoom) => {
    if (room.type === 'applicant_admin') {
      return 'Application Discussion';
    }
    return 'Admin Team Chat';
  };

  const formatLastMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = (now.getTime() - date.getTime()) / (1000 * 60);
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${Math.floor(diffInMinutes)}m ago`;
    
    const diffInHours = diffInMinutes / 60;
    if (diffInHours < 24) return `${Math.floor(diffInHours)}h ago`;
    
    const diffInDays = diffInHours / 24;
    if (diffInDays < 7) return `${Math.floor(diffInDays)}d ago`;
    
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Loading chat rooms...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
            <button
              onClick={() => {
                setError('');
                loadRooms(0);
              }}
              className="text-red-600 hover:text-red-800 text-sm underline"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (rooms.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center p-4">
        <div className="text-6xl mb-4">💬</div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          No Chat Rooms
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          {isAdmin 
            ? 'Enable chat for applicants to start conversations'
            : 'Wait for an admin to enable chat for your application'
          }
        </p>
        <button
          onClick={() => loadRooms(0)}
          className="mt-4 text-blue-600 hover:text-blue-800 text-sm underline"
        >
          Refresh
        </button>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Chat Rooms
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {rooms.length} active conversation{rooms.length !== 1 ? 's' : ''}
        </p>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {rooms.map((room) => (
          <motion.button
            key={room.id}
            onClick={() => onSelectRoom(room)}
            whileHover={{ backgroundColor: 'rgba(59, 130, 246, 0.05)' }}
            whileTap={{ scale: 0.98 }}
            className={`w-full p-4 text-left transition-colors ${
              selectedRoomId === room.id
                ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-600'
                : 'hover:bg-gray-50 dark:hover:bg-gray-700/50'
            }`}
          >
            <div className="flex items-start space-x-3">
              {/* Room Avatar */}
              <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-medium ${
                room.type === 'applicant_admin' 
                  ? 'bg-gradient-to-br from-blue-500 to-purple-600'
                  : 'bg-gradient-to-br from-green-500 to-teal-600'
              }`}>
                {room.type === 'applicant_admin' ? '👤' : '👥'}
              </div>

              {/* Room Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {getRoomDisplayName(room)}
                  </h3>
                  {room.last_message && (
                    <span className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0 ml-2">
                      {formatLastMessageTime(room.last_message.created_at)}
                    </span>
                  )}
                </div>
                
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">
                  {getRoomSubtitle(room)}
                </p>

                {room.last_message && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                    {room.last_message.sender_id === user?.id ? 'You: ' : ''}
                    {room.last_message.message_type === 'file' ? '📎 File' : room.last_message.message}
                  </p>
                )}

                {/* Unread Count */}
                {room.unread_count && room.unread_count > 0 && (
                  <div className="inline-flex items-center justify-center w-5 h-5 bg-red-500 text-white text-xs rounded-full mt-1">
                    {room.unread_count > 99 ? '99+' : room.unread_count}
                  </div>
                )}
              </div>
            </div>
          </motion.button>
        ))}
      </div>
    </div>
  );
}
