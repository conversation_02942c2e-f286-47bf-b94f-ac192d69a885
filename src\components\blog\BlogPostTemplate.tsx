'use client';

import { BlogPost } from '@/lib/supabase';
import DefaultTemplate from './templates/DefaultTemplate';
import TutorialTemplate from './templates/TutorialTemplate';
import CaseStudyTemplate from './templates/CaseStudyTemplate';
import NewsTemplate from './templates/NewsTemplate';
import ProjectShowcaseTemplate from './templates/ProjectShowcaseTemplate';

interface BlogPostTemplateProps {
  post: BlogPost;
}

export default function BlogPostTemplate({ post }: BlogPostTemplateProps) {
  const renderTemplate = () => {
    switch (post.template) {
      case 'tutorial':
        return <TutorialTemplate post={post} />;
      case 'case-study':
        return <CaseStudyTemplate post={post} />;
      case 'news':
        return <NewsTemplate post={post} />;
      case 'project-showcase':
        return <ProjectShowcaseTemplate post={post} />;
      default:
        return <DefaultTemplate post={post} />;
    }
  };

  return (
    <div className="blog-post-template">
      {renderTemplate()}
      
      {/* Custom CSS */}
      {post.custom_css && (
        <style dangerouslySetInnerHTML={{ __html: post.custom_css }} />
      )}
      
      {/* Custom JS */}
      {post.custom_js && (
        <script dangerouslySetInnerHTML={{ __html: post.custom_js }} />
      )}
    </div>
  );
}
