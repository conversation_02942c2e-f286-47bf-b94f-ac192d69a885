{"name": "hieltech", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next/eslint-plugin-next": "^15.3.3", "@popperjs/core": "^2.11.8", "@supabase/supabase-js": "^2.39.0", "@tiptap/extension-code-block-lowlight": "^2.14.0", "@tiptap/extension-floating-menu": "^2.14.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "framer-motion": "^12.6.2", "highlight.js": "^11.11.1", "lowlight": "^3.3.0", "next": "^15.3.3", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}