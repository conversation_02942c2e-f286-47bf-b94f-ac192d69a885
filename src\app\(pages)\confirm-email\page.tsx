'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { supabase } from '@/lib/supabase';
import styles from '@/components/animations/auth.module.css';

// Component that uses useSearchParams - needs to be wrapped in Suspense
function ConfirmEmailContent() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleEmailConfirmation = async () => {
      try {
        // Check if we have the hash fragment (Supabase uses URL fragments for auth)
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = hashParams.get('access_token');
        const refreshToken = hashParams.get('refresh_token');
        const type = hashParams.get('type');

        // Also check URL search params as fallback
        const token = searchParams.get('token');
        const tokenHash = searchParams.get('token_hash');

        if (accessToken && refreshToken && type === 'signup') {
          // Handle URL fragment-based confirmation (newer Supabase)
          const { data, error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          });

          if (error) {
            throw error;
          }

          if (data.user) {
            setStatus('success');
            setMessage('Email confirmed successfully! Redirecting to your profile...');

            // Redirect to profile after 3 seconds
            setTimeout(() => {
              router.push('/profile');
            }, 3000);
          } else {
            throw new Error('No user data received');
          }
        } else if (token || tokenHash) {
          // Handle token-based confirmation (fallback)
          const { data, error } = await supabase.auth.verifyOtp({
            token_hash: tokenHash || token || '',
            type: 'signup'
          });

          if (error) {
            throw error;
          }

          if (data.user) {
            setStatus('success');
            setMessage('Email confirmed successfully! Redirecting to your profile...');

            // Redirect to profile after 3 seconds
            setTimeout(() => {
              router.push('/profile');
            }, 3000);
          } else {
            throw new Error('No user data received');
          }
        } else {
          setStatus('error');
          setMessage('Invalid confirmation link. Please try signing up again.');
        }

      } catch (error: unknown) {
        console.error('Email confirmation error:', error);
        setStatus('error');

        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage?.includes('expired')) {
          setMessage('Confirmation link has expired. Please sign up again.');
        } else if (errorMessage?.includes('invalid')) {
          setMessage('Invalid confirmation link. Please check your email or sign up again.');
        } else {
          setMessage('Failed to confirm email. Please try again or contact support.');
        }
      }
    };

    handleEmailConfirmation();
  }, [searchParams, router]);

  const handleReturnToSignup = () => {
    router.push('/login');
  };

  const handleGoToProfile = () => {
    router.push('/profile');
  };

  return (
    <div className="min-h-screen py-12 bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className={`max-w-md mx-auto p-8 rounded-xl shadow-2xl border border-white/20 dark:border-gray-700/30 transition-all duration-300 ${styles['glass-morphism']} bg-white/10 dark:bg-gray-800/30`}
      >
        {/* Status Icon */}
        <div className="text-center mb-6">
          <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
            status === 'loading' 
              ? 'bg-gradient-to-r from-blue-500 to-purple-600' 
              : status === 'success'
              ? 'bg-gradient-to-r from-green-500 to-emerald-600'
              : 'bg-gradient-to-r from-red-500 to-pink-600'
          }`}>
            {status === 'loading' && (
              <svg className="w-8 h-8 text-white animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            
            {status === 'success' && (
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            )}
            
            {status === 'error' && (
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            )}
          </div>
          
          <h2 className={`text-3xl font-bold bg-gradient-to-r ${
            status === 'success' 
              ? 'from-green-500 to-emerald-500' 
              : status === 'error'
              ? 'from-red-500 to-pink-500'
              : 'from-indigo-500 to-purple-500'
          } bg-clip-text text-transparent mb-4 ${styles['animate-fade-in']}`}>
            {status === 'loading' && 'Confirming Email...'}
            {status === 'success' && 'Email Confirmed!'}
            {status === 'error' && 'Confirmation Failed'}
          </h2>
        </div>

        {/* Message */}
        <div className="text-center mb-6">
          <p className="text-gray-600 dark:text-gray-300">
            {message}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          {status === 'success' && (
            <button
              onClick={handleGoToProfile}
              className="w-full py-3 px-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg font-medium hover:from-green-700 hover:to-emerald-700 transform hover:scale-[1.02] transition-all duration-200"
            >
              Go to Profile
            </button>
          )}
          
          {status === 'error' && (
            <button
              onClick={handleReturnToSignup}
              className="w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transform hover:scale-[1.02] transition-all duration-200"
            >
              Return to Sign Up
            </button>
          )}
          
          {status === 'loading' && (
            <div className="w-full py-3 px-4 bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded-lg font-medium text-center">
              Please wait...
            </div>
          )}
        </div>

        {/* Help Text */}
        {status === 'error' && (
          <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
            <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
              Need help?
            </h4>
            <ul className="text-xs text-red-700 dark:text-red-300 space-y-1">
              <li>• Make sure you clicked the correct link from your email</li>
              <li>• Check if the link has expired (links expire after 24 hours)</li>
              <li>• Try signing up again with the same email</li>
              <li>• Contact support if the problem persists</li>
            </ul>
          </div>
        )}
      </motion.div>
    </div>
  );
}

// Loading component for Suspense fallback
function ConfirmEmailLoading() {
  return (
    <div className="min-h-screen py-12 bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className={`max-w-md mx-auto p-8 rounded-xl shadow-2xl border border-white/20 dark:border-gray-700/30 transition-all duration-300 ${styles['glass-morphism']} bg-white/10 dark:bg-gray-800/30`}
      >
        <div className="text-center mb-6">
          <div className="mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 bg-gradient-to-r from-blue-500 to-purple-600">
            <svg className="w-8 h-8 text-white animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-indigo-500 to-purple-500 bg-clip-text text-transparent mb-4">
            Loading...
          </h2>
        </div>
        <div className="text-center mb-6">
          <p className="text-gray-600 dark:text-gray-300">
            Preparing email confirmation...
          </p>
        </div>
      </motion.div>
    </div>
  );
}

// Main export component with Suspense boundary
export default function ConfirmEmailPage() {
  return (
    <Suspense fallback={<ConfirmEmailLoading />}>
      <ConfirmEmailContent />
    </Suspense>
  );
}
