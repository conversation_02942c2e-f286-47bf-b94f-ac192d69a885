'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { db, ChatRoom, TeamApplication, ChatPermission } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';
import Chat from './Chat';

interface ApplicantChatProps {
  className?: string;
}

export default function ApplicantChat({ className = '' }: ApplicantChatProps) {
  const { user } = useAuth();
  const [application, setApplication] = useState<TeamApplication | null>(null);
  const [chatPermission, setChatPermission] = useState<ChatPermission | null>(null);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Load applicant data and chat permissions
  useEffect(() => {
    const loadData = async () => {
      if (!user?.id) return;

      try {
        setLoading(true);
        
        // Find the user's team application
        const applications = await db.getTeamApplications();
        const userApplication = applications.find(app => app.user_id === user.id);
        
        if (userApplication) {
          setApplication(userApplication);
          
          // Check chat permissions
          const permissions = await db.getChatPermissions(userApplication.id);
          const userPermission = permissions.find(p => p.user_id === user.id);
          setChatPermission(userPermission || null);
          
          // Load chat rooms if chat is enabled
          if (userPermission?.is_enabled) {
            const rooms = await db.getChatRooms(user.id, 'applicant_admin');
            setChatRooms(rooms);
          }
        }
      } catch (error) {
        console.error('Error loading applicant chat data:', error);
        setError('Failed to load chat data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [user?.id]);

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  // No application found
  if (!application) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`bg-white/10 dark:bg-gray-800/30 rounded-xl p-6 text-center ${className}`}
      >
        <div className="text-6xl mb-4">📝</div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          No Application Found
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          You haven&apos;t submitted a team application yet.
        </p>
        <a
          href="/team/join-us"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <span className="mr-2">📋</span>
          Apply to Join Team
        </a>
      </motion.div>
    );
  }

  // Application exists but account not linked
  if (!application.account_created || !application.user_id) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`bg-white/10 dark:bg-gray-800/30 rounded-xl p-6 text-center ${className}`}
      >
        <div className="text-6xl mb-4">🔗</div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Account Linking Required
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Your application exists but isn&apos;t linked to your account. Please contact support.
        </p>
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            <strong>Application Status:</strong> {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
          </p>
        </div>
      </motion.div>
    );
  }

  // Chat not enabled
  if (!chatPermission?.is_enabled) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`bg-white/10 dark:bg-gray-800/30 rounded-xl p-6 text-center ${className}`}
      >
        <div className="text-6xl mb-4">💬</div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Chat Not Available
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Chat functionality hasn&apos;t been enabled for your application yet.
        </p>
        
        <div className="space-y-3">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="text-sm space-y-2">
              <div className="flex justify-between">
                <span className="text-blue-700 dark:text-blue-300 font-medium">Application Status:</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  application.status === 'approved' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : application.status === 'reviewing'
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}>
                  {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-700 dark:text-blue-300 font-medium">Applied:</span>
                <span className="text-blue-600 dark:text-blue-400">
                  {new Date(application.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
          
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Our admin team will enable chat when they&apos;re ready to discuss your application.
          </p>
        </div>
      </motion.div>
    );
  }

  // Chat enabled - show chat interface
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white/10 dark:bg-gray-800/30 rounded-xl overflow-hidden ${className}`}
    >
      {/* Header */}
      <div className="p-4 border-b border-white/10 dark:border-gray-700/50">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Application Chat
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Discuss your application with our team
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2 py-1 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 text-xs rounded-full">
              <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></span>
              Chat Enabled
            </span>
          </div>
        </div>
      </div>

      {/* Chat Interface */}
      <div className="h-[500px]">
        <Chat className="h-full" />
      </div>

      {/* Footer Info */}
      <div className="p-3 bg-gray-50/50 dark:bg-gray-900/50 border-t border-white/10 dark:border-gray-700/50">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>Application: {application.desired_role}</span>
          <span>
            Chat enabled: {chatPermission.enabled_at ? new Date(chatPermission.enabled_at).toLocaleDateString() : 'Unknown'}
          </span>
        </div>
      </div>
    </motion.div>
  );
}
