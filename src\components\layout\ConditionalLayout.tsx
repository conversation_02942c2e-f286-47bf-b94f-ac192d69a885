'use client';

import { usePathname } from 'next/navigation';
import Navbar from './Navbar';
import Footer from './Footer';
import ChatWidget from '@/components/chat/ChatWidget';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  const isHielLinksPage = pathname?.startsWith('/hielLinks/');

  if (isHielLinksPage) {
    // HielLinks pages: clean layout without navbar/footer
    return <>{children}</>;
  }

  // Regular pages: full layout with navbar and footer
  return (
    <>
      <Navbar />
      <main className="flex-grow pt-16">{children}</main>
      <Footer />
      <ChatWidget />
    </>
  );
} 