'use client';

import Link from 'next/link';
import { useState } from 'react';

interface ProjectCardProps {
  id: string;
  title: string;
  description: string;
  category: string;
  technologies: string[];
  gradient?: string;
}

export default function ProjectCard({ 
  id, 
  title, 
  description, 
  category, 
  technologies,
  gradient = 'from-blue-500 via-indigo-500 to-purple-600'
}: ProjectCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  
  // Generate initials from title for placeholder
  const initials = title
    .split(' ')
    .map(word => word[0])
    .join('')
    .slice(0, 2)
    .toUpperCase();

  return (
    <div 
      className="group relative bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 ease-out transform hover:-translate-y-2 hover:scale-[1.02]"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Project Image/Placeholder */}
      <div 
        className={`h-64 relative overflow-hidden bg-gradient-to-br ${gradient} transition-transform duration-500 ease-out group-hover:scale-[1.03]`}
      >
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute inset-0 flex items-center justify-center text-white text-5xl font-bold tracking-wider transition-transform duration-500 transform group-hover:scale-110">
          {initials}
        </div>
        
        {/* Overlay on hover */}
        <div 
          className={`absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent flex items-center justify-center backdrop-blur-sm transition-all duration-500 ${isHovered ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
        >
          <Link 
            href={`/projects/${id}`}
            className="px-8 py-3 rounded-full bg-white/90 text-blue-600 font-semibold hover:bg-white hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            View Details
          </Link>
        </div>
      </div>
      
      {/* Project Info */}
      <div className="p-8">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-2xl font-bold tracking-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
            {title}
          </h3>
          <span className="text-xs font-medium px-3 py-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-600 dark:text-blue-400 transition-colors duration-300">
            {category}
          </span>
        </div>
        
        <p className="text-gray-600 dark:text-gray-300 mb-6 line-clamp-3 leading-relaxed">
          {description}
        </p>
        
        {/* Technologies */}
        <div className="flex flex-wrap gap-2">
          {technologies.map((tech, index) => (
            <span 
              key={index}
              className="text-xs font-medium px-3 py-1.5 bg-gray-100 dark:bg-gray-700/50 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-300"
            >
              {tech}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}
