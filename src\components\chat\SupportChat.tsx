'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { db, ChatRoom, Inquiry, ChatPermission } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';
import Chat from './Chat';

interface SupportChatProps {
  className?: string;
}

export default function SupportChat({ className = '' }: SupportChatProps) {
  const { user } = useAuth();
  const [inquiry, setInquiry] = useState<Inquiry | null>(null);
  const [chatPermission, setChatPermission] = useState<ChatPermission | null>(null);
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Load user's inquiry and chat permissions
  useEffect(() => {
    const loadData = async () => {
      if (!user?.id) return;

      try {
        setLoading(true);
        
        // Find the user's inquiry (match by email since they submitted the inquiry before creating account)
        const profile = await db.getProfile(user.id);
        if (!profile?.email) {
          setError('User profile not found');
          return;
        }

        // Get all inquiries and find one matching user's email
        const inquiries = await db.getInquiries();
        const userInquiry = inquiries.find(inq => inq.email === profile.email);
        
        if (userInquiry) {
          setInquiry(userInquiry);
          
          // Check chat permissions for this inquiry
          const permissions = await db.getChatPermissions(userInquiry.id);
          const userPermission = permissions.find(p => p.user_id === user.id);
          setChatPermission(userPermission || null);
          
          // Load chat rooms if chat is enabled
          if (userPermission?.is_enabled) {
            const rooms = await db.getChatRooms(user.id, 'applicant_admin');
            const supportRooms = rooms.filter(room => room.application_id === userInquiry.id);
            setChatRooms(supportRooms);
          }
        }
      } catch (error) {
        console.error('Error loading support chat data:', error);
        setError('Failed to load chat data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [user?.id, user]);

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`bg-white/10 dark:bg-gray-800/30 rounded-xl p-6 ${className}`}
      >
        <div className="text-center">
          <div className="text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Error Loading Support Chat
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {error}
          </p>
        </div>
      </motion.div>
    );
  }

  // No inquiry found
  if (!inquiry) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`bg-white/10 dark:bg-gray-800/30 rounded-xl p-6 ${className}`}
      >
        <div className="text-center">
          <div className="text-4xl mb-4">💬</div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            No Support Requests Found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            You haven't submitted any inquiries yet. Contact us through our contact form to start a conversation.
          </p>
          <a
            href="/contact"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Contact Us
          </a>
        </div>
      </motion.div>
    );
  }

  // Chat not enabled
  if (!chatPermission?.is_enabled) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`bg-white/10 dark:bg-gray-800/30 rounded-xl p-6 ${className}`}
      >
        <div className="text-center">
          <div className="text-4xl mb-4">⏳</div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Support Chat Not Available
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Your inquiry has been received. Our support team will enable chat when they're ready to assist you.
          </p>
          
          <div className="bg-gray-50/50 dark:bg-gray-900/50 rounded-lg p-4 text-left">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Your Inquiry:</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              <strong>Subject:</strong> {inquiry.subject || 'General Inquiry'}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              <strong>Type:</strong> {inquiry.type === 'service' ? 'Service Request' : 'General Contact'}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Submitted:</strong> {new Date(inquiry.created_at).toLocaleDateString()}
            </p>
          </div>
          
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
            We'll notify you via email when chat support is available.
          </p>
        </div>
      </motion.div>
    );
  }

  // Chat enabled - show chat interface
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white/10 dark:bg-gray-800/30 rounded-xl overflow-hidden ${className}`}
    >
      {/* Header */}
      <div className="p-4 border-b border-white/10 dark:border-gray-700/50">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Support Chat
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Get help with your inquiry: {inquiry.subject || 'General Support'}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2 py-1 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 text-xs rounded-full">
              <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></span>
              Support Available
            </span>
          </div>
        </div>
      </div>

      {/* Chat Interface */}
      <div className="h-[500px]">
        <Chat className="h-full" />
      </div>

      {/* Footer Info */}
      <div className="p-3 bg-gray-50/50 dark:bg-gray-900/50 border-t border-white/10 dark:border-gray-700/50">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>
            Inquiry Type: {inquiry.type === 'service' ? 'Service Request' : 'General Contact'}
          </span>
          <span>
            Support enabled: {chatPermission.enabled_at ? new Date(chatPermission.enabled_at).toLocaleDateString() : 'Unknown'}
          </span>
        </div>
      </div>
    </motion.div>
  );
} 