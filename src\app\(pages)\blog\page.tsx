import Link from "next/link";
import Image from "next/image";
import { db, BlogPost } from "@/lib/supabase";

interface BlogPageProps {
  searchParams: Promise<{ category?: string; tag?: string; search?: string }>;
}

export default async function BlogPage({ searchParams }: BlogPageProps) {
  // Await searchParams before using
  const params = await searchParams;

  // Fetch blog posts from database
  const posts = await db.getBlogPosts({
    status: 'published',
    limit: 50
  });

  // Fetch categories for filtering
  const categories = await db.getCategories();

  // Filter posts based on search params
  let filteredPosts = posts;

  if (params.category) {
    const category = categories.find(c => c.slug === params.category);
    if (category) {
      filteredPosts = filteredPosts.filter(p => p.category_id === category.id);
    }
  }

  if (params.search) {
    const searchTerm = params.search.toLowerCase();
    filteredPosts = filteredPosts.filter(p =>
      p.title.toLowerCase().includes(searchTerm) ||
      p.excerpt?.toLowerCase().includes(searchTerm) ||
      p.content.toLowerCase().includes(searchTerm)
    );
  }
  
  // Get featured post (first published featured post or first post)
  const featuredPost = filteredPosts.find(p => p.is_featured) || filteredPosts[0];
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const stripHtml = (html: string) => {
    return html.replace(/<[^>]*>/g, '');
  };

  const getExcerpt = (post: BlogPost) => {
    if (post.excerpt) return post.excerpt;
    return stripHtml(post.content).substring(0, 150) + '...';
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 md:py-28 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl font-bold tracking-tight animate-fade-in">
              Our <span className="text-blue-600 dark:text-blue-400">Blog</span>
            </h1>
            <p className="mt-6 text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto animate-slide-up">
              Insights, tutorials, and updates from the Hiel Tech team
            </p>
          </div>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          {/* Category Filters */}
          <div className="mb-12 flex flex-wrap justify-center gap-4">
            <Link
              href="/blog"
              className={`px-4 py-2 rounded-full font-medium transition-colors ${
                !params.category
                  ? 'bg-blue-600 text-white'
                  : 'border border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800'
              }`}
            >
              All Posts ({posts.length})
            </Link>
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/blog?category=${category.slug}`}
                className={`px-4 py-2 rounded-full font-medium transition-colors flex items-center ${
                  params.category === category.slug
                    ? 'bg-blue-600 text-white'
                    : 'border border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
              >
                {category.icon && <span className="mr-1">{category.icon}</span>}
                {category.name}
              </Link>
            ))}
          </div>
          
          {/* Search Results Info */}
          {params.search && (
            <div className="mb-8 text-center">
              <p className="text-gray-600 dark:text-gray-400">
                Found {filteredPosts.length} posts for &ldquo;{params.search}&rdquo;
              </p>
            </div>
          )}

          {/* No Posts Message */}
          {filteredPosts.length === 0 && (
            <div className="text-center py-16">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No posts found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {params.search
                  ? "Try adjusting your search terms or browse all posts."
                  : "No posts have been published yet."
                }
              </p>
              <Link
                href="/blog"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                View All Posts
              </Link>
            </div>
          )}

          {/* Featured Post */}
          {featuredPost && filteredPosts.length > 0 && (
            <div className="mb-16">
              <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md">
                <div className="md:flex">
                  <div className="md:w-1/2 h-64 md:h-auto bg-gray-200 dark:bg-gray-700 relative">
                    {featuredPost.featured_image ? (
                      <Image
                        src={featuredPost.featured_image.file_path}
                        alt={featuredPost.featured_image.alt_text || featuredPost.title}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center text-gray-500 dark:text-gray-400 text-xl font-semibold">
                        Featured Post
                      </div>
                    )}
                    {featuredPost.is_featured && (
                      <div className="absolute top-4 left-4 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                        Featured
                      </div>
                    )}
                  </div>
                  <div className="md:w-1/2 p-8">
                    <div className="flex items-center mb-4">
                      <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                        {featuredPost.category?.name || 'Uncategorized'}
                      </span>
                      <span className="mx-2 text-gray-400">•</span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(featuredPost.published_at || featuredPost.created_at)}
                      </span>
                      <span className="mx-2 text-gray-400">•</span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {featuredPost.reading_time} min read
                      </span>
                    </div>
                    <h2 className="text-2xl font-bold mb-4">{featuredPost.title}</h2>
                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                      {getExcerpt(featuredPost)}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 mr-3">
                          {featuredPost.author?.display_name 
                            ? featuredPost.author.display_name.split(' ').map(n => n[0]).join('')
                            : 'A'
                          }
                        </div>
                        <div>
                          <p className="font-medium">
                            {featuredPost.author?.display_name || 'Admin'}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {featuredPost.view_count} views
                          </p>
                        </div>
                      </div>
                      <Link 
                        href={`/blog/${featuredPost.slug}`}
                        className="px-4 py-2 rounded-full bg-blue-600 text-white font-medium hover:bg-blue-700 transition-colors"
                      >
                        Read More
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Blog Posts Grid */}
          {filteredPosts.length > 1 && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.slice(featuredPost ? 1 : 0).map((post) => (
                <div 
                  key={post.id}
                  className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 flex flex-col"
                >
                  {/* Post Image */}
                  <div className="h-48 bg-gray-200 dark:bg-gray-700 relative">
                    {post.featured_image ? (
                      <Image
                        src={post.featured_image.file_path}
                        alt={post.featured_image.alt_text || post.title}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center text-gray-500 dark:text-gray-400">
                        Blog Post
                      </div>
                    )}
                  </div>
                  
                  {/* Post Info */}
                  <div className="p-6 flex-grow">
                    <div className="flex items-center mb-3">
                      <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
                        {post.category?.name || 'Uncategorized'}
                      </span>
                      <span className="mx-2 text-gray-400">•</span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {post.reading_time} min read
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-semibold mb-3 line-clamp-2">{post.title}</h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                      {getExcerpt(post)}
                    </p>
                    
                    {/* Tags */}
                    {post.tags && post.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-4">
                        {post.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag.id}
                            className="px-2 py-1 rounded text-xs"
                            style={{ 
                              backgroundColor: tag.color + '20',
                              color: tag.color
                            }}
                          >
                            {tag.name}
                          </span>
                        ))}
                        {post.tags.length > 3 && (
                          <span className="px-2 py-1 rounded text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
                            +{post.tags.length - 3}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  
                  {/* Author and Date */}
                  <div className="px-6 pb-6 mt-auto">
                    <div className="pt-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 text-xs mr-2">
                          {post.author?.display_name 
                            ? post.author.display_name.split(' ').map(n => n[0]).join('')
                            : 'A'
                          }
                        </div>
                        <div>
                          <p className="text-sm font-medium">
                            {post.author?.display_name || 'Admin'}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(post.published_at || post.created_at)}
                          </p>
                        </div>
                      </div>
                      <Link 
                        href={`/blog/${post.slug}`}
                        className="text-blue-600 dark:text-blue-400 hover:underline text-sm"
                      >
                        Read →
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900/50">
        <div className="container mx-auto">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">Subscribe to Our Newsletter</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-8">
              Stay updated with our latest insights, tutorials, and industry news
            </p>
            <form className="flex flex-col sm:flex-row gap-4">
              <input 
                type="email" 
                placeholder="Enter your email" 
                className="flex-grow px-4 py-3 rounded-full border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-600 dark:focus:ring-blue-500"
                required
              />
              <button 
                type="submit"
                className="px-6 py-3 rounded-full bg-blue-600 text-white font-medium hover:bg-blue-700 transition-colors"
              >
                Subscribe
              </button>
            </form>
            <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
              We respect your privacy. Unsubscribe at any time.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
