'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { db, TeamMember, TeamApplication } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';
import TeamMemberList from './TeamMemberList';
import TeamMemberEditor from './TeamMemberEditor';
import TeamApplications from './TeamApplications';
import AdminChat from './AdminChat';

type TabType = 'members' | 'editor' | 'applications' | 'chat';

export default function TeamManagement() {
  const { isAdmin } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('members');
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [applications, setApplications] = useState<TeamApplication[]>([]);
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isAdmin) {
      loadData();
    }
  }, [isAdmin]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [membersData, applicationsData] = await Promise.all([
        db.getTeamMembers(false), // Get all members including inactive
        db.getTeamApplications()
      ]);
      
      setTeamMembers(membersData);
      setApplications(applicationsData);
    } catch (error) {
      console.error('Error loading team data:', error);
      setError('Failed to load team data');
    } finally {
      setLoading(false);
    }
  };

  const handleMemberSaved = async () => {
    await loadData();
    setActiveTab('members');
    setSelectedMember(null);
  };

  const handleMemberDeleted = async () => {
    await loadData();
  };

  const handleEditMember = (member: TeamMember) => {
    setSelectedMember(member);
    setActiveTab('editor');
  };

  const handleNewMember = () => {
    setSelectedMember(null);
    setActiveTab('editor');
  };

  const handleApplicationUpdated = async () => {
    await loadData();
  };

  if (!isAdmin) {
    return (
      <div className="p-6 text-center">
        <p className="text-red-600 dark:text-red-400">
          You do not have permission to access team management.
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const tabs = [
    {
      id: 'members' as TabType,
      label: 'Team Members',
      icon: '👥',
      count: teamMembers.length
    },
    {
      id: 'editor' as TabType,
      label: selectedMember ? 'Edit Member' : 'Add Member',
      icon: selectedMember ? '✏️' : '➕'
    },
    {
      id: 'applications' as TabType,
      label: 'Applications',
      icon: '📝',
      count: applications.filter(app => app.status === 'pending').length
    },
    {
      id: 'chat' as TabType,
      label: 'Admin Chat',
      icon: '💬'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
            Team Management
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1 text-sm sm:text-base">
            Manage team members and review applications
          </p>
        </div>
        <motion.button
          onClick={handleNewMember}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center shadow-md hover:shadow-lg"
        >
          <span className="mr-2">➕</span>
          <span className="hidden sm:inline">Add Member</span>
          <span className="sm:hidden">Add</span>
        </motion.button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 overflow-x-auto">
        <nav className="-mb-px flex space-x-4 sm:space-x-8 min-w-max">
          {tabs.map((tab) => (
            <motion.button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm transition-colors duration-200 whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <span className="flex items-center">
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
                {tab.count !== undefined && (
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                    activeTab === tab.id
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </span>
            </motion.button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'members' && (
          <TeamMemberList
            members={teamMembers}
            onEdit={handleEditMember}
            onDelete={handleMemberDeleted}
            onRefresh={loadData}
          />
        )}
        {activeTab === 'editor' && (
          <TeamMemberEditor
            member={selectedMember}
            onSave={handleMemberSaved}
            onCancel={() => {
              setActiveTab('members');
              setSelectedMember(null);
            }}
          />
        )}
        {activeTab === 'applications' && (
          <TeamApplications
            applications={applications}
            onUpdate={handleApplicationUpdated}
          />
        )}
        {activeTab === 'chat' && (
          <AdminChat />
        )}
      </motion.div>
    </div>
  );
}
