'use client';

import { useState } from 'react';

interface SocialShareProps {
  title: string;
  url: string;
  description?: string;
  className?: string;
}

export default function SocialShare({ title, url, description, className = '' }: SocialShareProps) {
  const [copied, setCopied] = useState(false);

  const shareData = {
    title,
    url,
    text: description || title
  };

  const handleShare = async (platform: string) => {
    const encodedTitle = encodeURIComponent(title);
    const encodedUrl = encodeURIComponent(url);
    const encodedDescription = encodeURIComponent(description || title);

    let shareUrl = '';

    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodedTitle}&url=${encodedUrl}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`;
        break;
      case 'reddit':
        shareUrl = `https://reddit.com/submit?url=${encodedUrl}&title=${encodedTitle}`;
        break;
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`;
        break;
      case 'telegram':
        shareUrl = `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`;
        break;
      case 'email':
        shareUrl = `mailto:?subject=${encodedTitle}&body=${encodedDescription}%0A%0A${encodedUrl}`;
        break;
      case 'copy':
        try {
          await navigator.clipboard.writeText(url);
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
          return;
        } catch (err) {
          console.error('Failed to copy:', err);
          return;
        }
      case 'native':
        if (navigator.share) {
          try {
            await navigator.share(shareData);
            return;
          } catch (err) {
            console.error('Error sharing:', err);
            return;
          }
        }
        break;
    }

    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  const shareButtons = [
    { id: 'twitter', label: 'Twitter', icon: '🐦', color: 'hover:bg-blue-50 dark:hover:bg-blue-900/20' },
    { id: 'linkedin', label: 'LinkedIn', icon: '💼', color: 'hover:bg-blue-50 dark:hover:bg-blue-900/20' },
    { id: 'facebook', label: 'Facebook', icon: '📘', color: 'hover:bg-blue-50 dark:hover:bg-blue-900/20' },
    { id: 'reddit', label: 'Reddit', icon: '🔴', color: 'hover:bg-orange-50 dark:hover:bg-orange-900/20' },
    { id: 'whatsapp', label: 'WhatsApp', icon: '💬', color: 'hover:bg-green-50 dark:hover:bg-green-900/20' },
    { id: 'telegram', label: 'Telegram', icon: '✈️', color: 'hover:bg-blue-50 dark:hover:bg-blue-900/20' },
    { id: 'email', label: 'Email', icon: '📧', color: 'hover:bg-gray-50 dark:hover:bg-gray-700' },
    { id: 'copy', label: copied ? 'Copied!' : 'Copy Link', icon: copied ? '✅' : '🔗', color: 'hover:bg-gray-50 dark:hover:bg-gray-700' },
  ];

  // Add native share if supported
  if (typeof navigator !== 'undefined' && 'share' in navigator) {
    shareButtons.unshift({
      id: 'native',
      label: 'Share',
      icon: '📤',
      color: 'hover:bg-purple-50 dark:hover:bg-purple-900/20'
    });
  }

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {shareButtons.map((button) => (
        <button
          key={button.id}
          onClick={() => handleShare(button.id)}
          className={`flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-lg border border-gray-300 dark:border-gray-600 transition-colors ${button.color}`}
          title={`Share on ${button.label}`}
        >
          <span className="mr-2">{button.icon}</span>
          <span className="hidden sm:inline">{button.label}</span>
        </button>
      ))}
    </div>
  );
}
