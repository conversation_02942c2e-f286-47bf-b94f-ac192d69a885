import Link from 'next/link';
import Image from 'next/image';
import { BlogPost } from '@/lib/supabase';

interface ProjectShowcaseTemplateProps {
  post: BlogPost;
}

export default function ProjectShowcaseTemplate({ post }: ProjectShowcaseTemplateProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Hero Section */}
      <header className="text-center mb-12">
        {/* Project Badge */}
        <div className="flex justify-center mb-6">
          <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 dark:from-purple-900/20 dark:to-pink-900/20 dark:text-purple-400">
            🚀 Project Showcase
          </span>
        </div>

        {/* Title */}
        <h1 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-8 leading-tight">
          {post.title}
        </h1>

        {/* Excerpt */}
        {post.excerpt && (
          <p className="text-2xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed max-w-4xl mx-auto">
            {post.excerpt}
          </p>
        )}

        {/* Project Actions */}
        <div className="flex flex-wrap justify-center gap-4 mb-8">
          {post.external_links?.demo && (
            <a
              href={post.external_links.demo}
              target="_blank"
              rel="noopener noreferrer"
              className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 font-medium shadow-lg"
            >
              🌐 View Live Demo
            </a>
          )}
          {post.external_links?.github && (
            <a
              href={post.external_links.github}
              target="_blank"
              rel="noopener noreferrer"
              className="px-8 py-3 bg-gray-800 dark:bg-gray-700 text-white rounded-lg hover:bg-gray-900 dark:hover:bg-gray-600 transition-colors font-medium shadow-lg"
            >
              💻 View Source
            </a>
          )}
          {post.external_links?.playstore && (
            <a
              href={post.external_links.playstore}
              target="_blank"
              rel="noopener noreferrer"
              className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium shadow-lg"
            >
              📱 Download App
            </a>
          )}
        </div>

        {/* Author */}
        <div className="flex items-center justify-center">
          <div className="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 mr-4">
            {post.author?.display_name 
              ? post.author.display_name.split(' ').map(n => n[0]).join('')
              : 'A'
            }
          </div>
          <div className="text-left">
            <p className="font-medium text-gray-900 dark:text-white">
              {post.author?.display_name || 'Admin'}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {formatDate(post.published_at || post.created_at)}
            </p>
          </div>
        </div>
      </header>

      {/* Featured Image/Video */}
      {post.featured_image &&
        <div className="mb-16 relative">
          <Image
            src={post.featured_image.file_path}
            alt={post.featured_image.alt_text || post.title}
            width={1200}
            height={600}
            className="w-full h-64 sm:h-80 md:h-96 lg:h-[500px] object-cover rounded-2xl shadow-2xl"
          />
          {post.featured_image.caption && (
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center mt-4">
              {post.featured_image.caption}
            </p>
          )}
        </div>
      }

      {/* Project Details Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <div className="prose prose-lg max-w-none dark:prose-invert prose-blue">
            <div dangerouslySetInnerHTML={{ __html: post.content }} />
          </div>
        </div>

        {/* Project Info Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* Tech Stack */}
            {post.tags && post.tags.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  🛠️ Tech Stack
                </h3>
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag) => (
                    <span
                      key={tag.id}
                      className="px-3 py-1 rounded-lg text-sm font-medium border"
                      style={{ 
                        backgroundColor: tag.color + '10',
                        borderColor: tag.color + '40',
                        color: tag.color
                      }}
                    >
                      {tag.name}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Project Stats */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📊 Project Stats
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Views</span>
                  <span className="font-medium">{post.view_count}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Reading Time</span>
                  <span className="font-medium">{post.reading_time} min</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Category</span>
                  <span className="font-medium">{post.category?.name || 'Project'}</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                🔗 Quick Links
              </h3>
              <div className="space-y-3">
                {post.external_links?.demo && (
                  <a
                    href={post.external_links.demo}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center text-sm"
                  >
                    🌐 Live Demo
                  </a>
                )}
                {post.external_links?.github && (
                  <a
                    href={post.external_links.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block w-full px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors text-center text-sm"
                  >
                    💻 Source Code
                  </a>
                )}
                {post.external_links?.playstore && (
                  <a
                    href={post.external_links.playstore}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-center text-sm"
                  >
                    📱 Play Store
                  </a>
                )}
                {post.external_links?.appstore && (
                  <a
                    href={post.external_links.appstore}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-center text-sm"
                  >
                    🍎 App Store
                  </a>
                )}
              </div>
            </div>

            {/* Share Project */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📤 Share Project
              </h3>
              <div className="space-y-2">
                <button 
                  onClick={() => {
                    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(`Check out this amazing project: ${post.title}`)}&url=${encodeURIComponent(window.location.href)}`;
                    window.open(url, '_blank');
                  }}
                  className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  🐦 Share on Twitter
                </button>
                <button 
                  onClick={() => {
                    const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`;
                    window.open(url, '_blank');
                  }}
                  className="w-full flex items-center justify-center px-4 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors text-sm"
                >
                  💼 Share on LinkedIn
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl p-8 mb-12">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Interested in Similar Projects?
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Explore more of our innovative solutions and case studies
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Link
            href="/blog"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            View All Projects
          </Link>
          <Link
            href="/contact"
            className="px-6 py-3 border border-blue-600 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
          >
            Start Your Project
          </Link>
        </div>
      </div>
    </div>
  );
}
