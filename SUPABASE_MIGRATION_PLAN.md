# Firebase to Supabase Migration Plan

## Overview
This document outlines the complete migration strategy from Firebase to Supabase for the Hiel Tech website.

## Current Firebase Setup Analysis

### 🔍 Current Firebase Components
1. **Authentication**: Firebase Auth with email/password
2. **Database**: Firestore with tasks collection
3. **Hosting**: Firebase Hosting (public folder)
4. **Security Rules**: Firestore rules for tasks collection
5. **Dependencies**: Multiple Firebase packages

### 📦 Current Firebase Dependencies
```json
"@firebase/app": "^0.11.3",
"@firebase/auth": "^1.9.1", 
"@firebase/firestore": "^4.7.10",
"firebase": "^11.5.0",
"react-firebase-hooks": "^5.1.1"
```

### 🗂️ Current Data Structure
- **Tasks Collection**: User-specific tasks with RLS
- **Users**: Managed by Firebase Auth
- **Admin System**: Basic email-based admin check

## Migration Strategy

### Phase 1: Environment Setup ✅
- [x] Supabase project exists: `cjfdzpiqgnxewhdivcrc`
- [x] Project region: `us-east-2`
- [x] Database version: PostgreSQL 15.8.1

### Phase 2: Database Schema Migration
1. **Create Tables**
   - `profiles` table for user data
   - `tasks` table (migrate from Firestore)
   - `user_roles` table for admin management

2. **Row Level Security (RLS)**
   - Enable RLS on all tables
   - Create policies matching current Firestore rules

### Phase 3: Authentication Migration
1. **Replace Firebase Auth with Supabase Auth**
   - Update AuthContext to use Supabase
   - Migrate authentication methods
   - Update login/signup flows

### Phase 4: Database Service Migration
1. **Replace Firestore with Supabase Database**
   - Update taskService to use Supabase client
   - Replace Firestore queries with SQL queries
   - Update real-time subscriptions

### Phase 5: Dependencies Update
1. **Remove Firebase Dependencies**
2. **Add Supabase Dependencies**
3. **Update package.json**

### Phase 6: Configuration & Environment
1. **Environment Variables**
   - Replace Firebase config with Supabase config
   - Update .env files
2. **Remove Firebase Files**
   - firebase.json
   - .firebaserc
   - firestore.rules
   - firestore.indexes.json

### Phase 7: Testing & Deployment
1. **Local Testing**
2. **Data Migration** (if needed)
3. **Production Deployment**

## Detailed Implementation Steps

### Step 1: Install Supabase Dependencies
```bash
npm install @supabase/supabase-js
npm uninstall firebase @firebase/app @firebase/auth @firebase/firestore react-firebase-hooks
```

### Step 2: Environment Configuration
```env
NEXT_PUBLIC_SUPABASE_URL=https://cjfdzpiqgnxewhdivcrc.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Step 3: Database Schema
```sql
-- Create profiles table
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE,
  email TEXT,
  display_name TEXT,
  role TEXT DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (id)
);

-- Create tasks table
CREATE TABLE tasks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'pending',
  priority TEXT DEFAULT 'medium',
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Step 4: Row Level Security Policies
```sql
-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Tasks policies
CREATE POLICY "Users can view own tasks" ON tasks
  FOR SELECT USING (auth.uid() = created_by);

CREATE POLICY "Users can create own tasks" ON tasks
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update own tasks" ON tasks
  FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete own tasks" ON tasks
  FOR DELETE USING (auth.uid() = created_by);
```

## Files to Modify

### 🔄 Core Files
1. `src/lib/firebase.ts` → `src/lib/supabase.ts`
2. `src/lib/auth/AuthContext.tsx`
3. `src/lib/services/taskService.ts`
4. `package.json`

### 🗑️ Files to Remove
1. `firebase.json`
2. `.firebaserc`
3. `firestore.rules`
4. `firestore.indexes.json`
5. `public/index.html` (Firebase hosting page)

### ➕ Files to Add
1. Environment variables for Supabase
2. Database migration scripts
3. Supabase configuration

## Risk Assessment

### 🔴 High Risk
- Data loss during migration
- Authentication session disruption
- Breaking changes in production

### 🟡 Medium Risk
- API rate limits during migration
- Real-time subscription changes
- Admin functionality updates

### 🟢 Low Risk
- UI/UX changes
- Performance improvements
- Cost optimization

## Rollback Plan
1. Keep Firebase project active during migration
2. Maintain dual authentication temporarily
3. Database backup before migration
4. Feature flags for gradual rollout

## Success Criteria
- [ ] All authentication flows working
- [ ] All database operations functional
- [ ] Real-time updates working
- [ ] Admin panel operational
- [ ] No data loss
- [ ] Performance maintained or improved

## Timeline Estimate
- **Phase 1-2**: 2-3 hours (Setup & Schema)
- **Phase 3-4**: 4-5 hours (Auth & Database Migration)
- **Phase 5-6**: 2-3 hours (Dependencies & Config)
- **Phase 7**: 2-3 hours (Testing & Deployment)
- **Total**: 10-14 hours

## Next Steps
1. Get Supabase project API keys
2. Create database schema
3. Begin authentication migration
4. Update services layer
5. Test thoroughly
6. Deploy to production
