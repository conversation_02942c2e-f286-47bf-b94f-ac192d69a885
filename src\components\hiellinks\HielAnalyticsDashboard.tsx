'use client';

import { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface AnalyticsData {
  profileViews: { date: string; views: number; uniqueViews: number }[];
  linkClicks: { linkId: string; linkTitle: string; clicks: number }[];
  referrers: { source: string; visits: number; percentage: number }[];
  devices: { type: string; count: number; percentage: number }[];
  countries: { country: string; visits: number; flag: string }[];
  timeMetrics: {
    avgSessionDuration: number;
    bounceRate: number;
    conversionRate: number;
    peakHours: number[];
  };
}

interface HielAnalyticsDashboardProps {
  profileId: string;
  timeRange: '7d' | '30d' | '90d' | '1y';
  onTimeRangeChange: (range: '7d' | '30d' | '90d' | '1y') => void;
}

export default function HielAnalyticsDashboard({ 
  profileId, 
  timeRange, 
  onTimeRangeChange 
}: HielAnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedMetric, setSelectedMetric] = useState<'views' | 'clicks' | 'referrers' | 'devices'>('views');
  const [isExporting, setIsExporting] = useState(false);
  const [liveMode, setLiveMode] = useState(false);

  // Simulated analytics data - replace with real API calls
  const mockAnalytics: AnalyticsData = useMemo(() => ({
    profileViews: Array.from({ length: 30 }, (_, i) => ({
      date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      views: Math.floor(Math.random() * 100) + 20,
      uniqueViews: Math.floor(Math.random() * 80) + 15,
    })),
    linkClicks: [
      { linkId: '1', linkTitle: 'Personal Website', clicks: 156 },
      { linkId: '2', linkTitle: 'LinkedIn Profile', clicks: 134 },
      { linkId: '3', linkTitle: 'GitHub Repository', clicks: 89 },
      { linkId: '4', linkTitle: 'Contact Email', clicks: 67 },
      { linkId: '5', linkTitle: 'Portfolio', clicks: 45 },
    ],
    referrers: [
      { source: 'Direct', visits: 245, percentage: 42 },
      { source: 'Google', visits: 123, percentage: 21 },
      { source: 'Social Media', visits: 89, percentage: 15 },
      { source: 'LinkedIn', visits: 67, percentage: 12 },
      { source: 'Other', visits: 58, percentage: 10 },
    ],
    devices: [
      { type: 'Mobile', count: 345, percentage: 58 },
      { type: 'Desktop', count: 201, percentage: 34 },
      { type: 'Tablet', count: 48, percentage: 8 },
    ],
    countries: [
      { country: 'United States', visits: 156, flag: '🇺🇸' },
      { country: 'Canada', visits: 89, flag: '🇨🇦' },
      { country: 'United Kingdom', visits: 67, flag: '🇬🇧' },
      { country: 'Germany', visits: 45, flag: '🇩🇪' },
      { country: 'Australia', visits: 34, flag: '🇦🇺' },
    ],
    timeMetrics: {
      avgSessionDuration: 145, // seconds
      bounceRate: 34, // percentage
      conversionRate: 12.5, // percentage
      peakHours: [10, 14, 19], // hours
    },
  }), []);

  useEffect(() => {
    const fetchAnalytics = async () => {
      setIsLoading(true);
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setAnalytics(mockAnalytics);
      setIsLoading(false);
    };

    fetchAnalytics();

    // Live mode updates
    if (liveMode) {
      const interval = setInterval(fetchAnalytics, 10000); // Update every 10 seconds
      return () => clearInterval(interval);
    }
  }, [profileId, timeRange, liveMode, mockAnalytics]);

  const totalViews = useMemo(() => 
    analytics?.profileViews.reduce((sum, day) => sum + day.views, 0) || 0,
    [analytics]
  );

  const totalClicks = useMemo(() => 
    analytics?.linkClicks.reduce((sum, link) => sum + link.clicks, 0) || 0,
    [analytics]
  );

  const handleExport = async () => {
    setIsExporting(true);
    
    // Simulate export process
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Create and download CSV
    const csvData = [
      ['Date', 'Views', 'Unique Views'],
      ...(analytics?.profileViews.map(day => [day.date, day.views, day.uniqueViews]) || [])
    ];
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics-${profileId}-${timeRange}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    setIsExporting(false);
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  const getMetricIcon = (metric: string) => {
    switch (metric) {
      case 'views': return '👁️';
      case 'clicks': return '🖱️';
      case 'referrers': return '🔗';
      case 'devices': return '📱';
      default: return '📊';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Loading Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
              </div>
            </div>
          ))}
        </div>
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="animate-pulse h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📊</div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          No Analytics Data Available
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Analytics data will appear here once your profile starts receiving visits.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Analytics Dashboard
          </h2>
          <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
            <span>Profile Analytics</span>
            {liveMode && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-green-600 dark:text-green-400">Live</span>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* Time Range Selector */}
          <select
            value={timeRange}
            onChange={(e) => onTimeRangeChange(e.target.value as '7d' | '30d' | '90d' | '1y')}
            className="px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>

          {/* Live Mode Toggle */}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setLiveMode(!liveMode)}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              liveMode 
                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
            }`}
          >
            {liveMode ? '🔴 Live' : '⏸️ Static'}
          </motion.button>

          {/* Export Button */}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleExport}
            disabled={isExporting}
            className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
          >
            {isExporting ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                <span>Exporting...</span>
              </>
            ) : (
              <>
                <span>📥</span>
                <span>Export</span>
              </>
            )}
          </motion.button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          {
            title: 'Total Views',
            value: totalViews.toLocaleString(),
            change: '+12.5%',
            trend: 'up',
            icon: '👁️',
            color: 'blue'
          },
          {
            title: 'Total Clicks',
            value: totalClicks.toLocaleString(),
            change: '+8.2%',
            trend: 'up',
            icon: '🖱️',
            color: 'green'
          },
          {
            title: 'Conversion Rate',
            value: `${analytics.timeMetrics.conversionRate}%`,
            change: '+3.1%',
            trend: 'up',
            icon: '🎯',
            color: 'purple'
          },
          {
            title: 'Avg. Session',
            value: formatDuration(analytics.timeMetrics.avgSessionDuration),
            change: '-2.4%',
            trend: 'down',
            icon: '⏱️',
            color: 'orange'
          },
        ].map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-lg bg-${metric.color}-100 dark:bg-${metric.color}-900/30`}>
                <span className="text-2xl">{metric.icon}</span>
              </div>
              <div className={`flex items-center space-x-1 text-sm ${
                metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                <span>{metric.trend === 'up' ? '↗️' : '↘️'}</span>
                <span>{metric.change}</span>
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                {metric.title}
              </p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">
                {metric.value}
              </p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Metric Selector */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-1">
        <div className="grid grid-cols-4 gap-1">
          {(['views', 'clicks', 'referrers', 'devices'] as const).map((metric) => (
            <motion.button
              key={metric}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setSelectedMetric(metric)}
              className={`flex items-center justify-center py-3 px-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                selectedMetric === metric
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              <span className="mr-2">{getMetricIcon(metric)}</span>
              {metric.charAt(0).toUpperCase() + metric.slice(1)}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Charts and Data Visualization */}
      <AnimatePresence mode="wait">
        <motion.div
          key={selectedMetric}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-6"
        >
          {/* Main Chart */}
          <div className="lg:col-span-2 bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <span className="mr-2">{getMetricIcon(selectedMetric)}</span>
              {selectedMetric === 'views' && 'Profile Views Over Time'}
              {selectedMetric === 'clicks' && 'Link Click Performance'}
              {selectedMetric === 'referrers' && 'Traffic Sources'}
              {selectedMetric === 'devices' && 'Device Distribution'}
            </h3>

            {selectedMetric === 'views' && (
              <div className="space-y-4">
                {/* Simple Bar Chart */}
                <div className="h-64 flex items-end space-x-1">
                  {analytics.profileViews.slice(-14).map((day) => (
                    <div key={day.date} className="flex-1 flex flex-col items-center">
                      <div
                        className="bg-blue-500 rounded-t w-full min-h-[4px] transition-all duration-300 hover:bg-blue-600"
                        style={{ height: `${(day.views / Math.max(...analytics.profileViews.map(d => d.views))) * 100}%` }}
                        title={`${day.date}: ${day.views} views`}
                      ></div>
                      <span className="text-xs text-gray-500 dark:text-gray-400 mt-1 transform -rotate-45 origin-top-left">
                        {new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {selectedMetric === 'clicks' && (
              <div className="space-y-4">
                {analytics.linkClicks.map((link, index) => (
                  <div key={link.linkId} className="flex items-center space-x-4">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {link.linkTitle}
                        </span>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {link.clicks} clicks
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${(link.clicks / Math.max(...analytics.linkClicks.map(l => l.clicks))) * 100}%` }}
                          transition={{ delay: index * 0.1, duration: 0.5 }}
                          className="bg-blue-500 h-2 rounded-full"
                        ></motion.div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {selectedMetric === 'referrers' && (
              <div className="space-y-4">
                {analytics.referrers.map((referrer) => (
                  <div key={referrer.source} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {referrer.source}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-bold text-gray-900 dark:text-white">
                        {referrer.visits}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {referrer.percentage}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {selectedMetric === 'devices' && (
              <div className="space-y-4">
                <div className="flex justify-center">
                  {/* Simple Pie Chart Representation */}
                  <div className="relative w-48 h-48">
                    {analytics.devices.map((device, index) => {
                      const colors = ['#3B82F6', '#10B981', '#F59E0B'];
                      const startAngle = analytics.devices.slice(0, index).reduce((sum, d) => sum + (d.percentage * 3.6), 0);
                      
                      return (
                        <div
                          key={device.type}
                          className="absolute inset-0 rounded-full border-8"
                          style={{
                            borderColor: colors[index],
                            borderTopColor: index === 0 ? colors[index] : 'transparent',
                            borderRightColor: index === 1 ? colors[index] : 'transparent',
                            borderBottomColor: index === 2 ? colors[index] : 'transparent',
                            borderLeftColor: 'transparent',
                            transform: `rotate(${startAngle}deg)`,
                          }}
                        ></div>
                      );
                    })}
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  {analytics.devices.map((device, index) => {
                    const colors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500'];
                    return (
                      <div key={device.type} className="text-center">
                        <div className={`w-4 h-4 ${colors[index]} rounded-full mx-auto mb-2`}></div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {device.type}
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          {device.percentage}%
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {/* Side Panel */}
          <div className="space-y-6">
            {/* Geographic Distribution */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <span className="mr-2">🌍</span>
                Top Countries
              </h3>
              <div className="space-y-3">
                {analytics.countries.map((country) => (
                  <motion.div
                    key={country.country}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{country.flag}</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {country.country}
                      </span>
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {country.visits}
                    </span>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Peak Hours */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <span className="mr-2">🕒</span>
                Peak Hours
              </h3>
              <div className="grid grid-cols-3 gap-3">
                {analytics.timeMetrics.peakHours.map((hour) => (
                  <motion.div
                    key={hour}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="text-center p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg"
                  >
                    <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                      {hour}:00
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      Peak time
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Additional Metrics */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <span className="mr-2">📈</span>
                Performance
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Bounce Rate</span>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {analytics.timeMetrics.bounceRate}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Conversion</span>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {analytics.timeMetrics.conversionRate}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Avg. Session</span>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {formatDuration(analytics.timeMetrics.avgSessionDuration)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
