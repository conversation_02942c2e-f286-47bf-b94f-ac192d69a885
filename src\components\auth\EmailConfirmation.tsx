'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { supabase } from '@/lib/supabase';
import styles from '@/components/animations/auth.module.css';

interface EmailConfirmationProps {
  email: string;
  onBack: () => void;
}

export default function EmailConfirmation({ email, onBack }: EmailConfirmationProps) {
  const [isResending, setIsResending] = useState(false);
  const [resendMessage, setResendMessage] = useState('');
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    // Start countdown timer
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleResendEmail = async () => {
    setIsResending(true);
    setResendMessage('');

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });

      if (error) {
        throw error;
      }

      setResendMessage('Confirmation email sent successfully!');
      setCanResend(false);
      setCountdown(60);
      
      // Restart countdown
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

    } catch (error: unknown) {
      console.error('Error resending email:', error);
      setResendMessage('Failed to resend email. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  const handleCheckEmail = () => {
    // Open default email client
    window.open('mailto:', '_blank');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`max-w-md mx-auto p-8 rounded-xl shadow-2xl border border-white/20 dark:border-gray-700/30 transition-all duration-300 hover:shadow-indigo-500/10 ${styles['glass-morphism']} bg-white/10 dark:bg-gray-800/30`}
    >
      {/* Email Icon */}
      <div className="text-center mb-6">
        <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
          <svg
            className="w-8 h-8 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            />
          </svg>
        </div>
        <h2 className={`text-3xl font-bold bg-gradient-to-r from-indigo-500 to-purple-500 bg-clip-text text-transparent mb-4 ${styles['animate-fade-in']}`}>
          Check Your Email
        </h2>
      </div>

      {/* Instructions */}
      <div className="text-center mb-6">
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          We&apos;ve sent a confirmation link to:
        </p>
        <p className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-4">
          {email}
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Click the link in the email to confirm your account and complete the registration process.
        </p>
      </div>

      {/* Action Buttons */}
      <div className="space-y-4">
        <button
          onClick={handleCheckEmail}
          className="w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transform hover:scale-[1.02] transition-all duration-200"
        >
          Open Email App
        </button>

        {/* Resend Email */}
        <div className="text-center">
          {canResend ? (
            <button
              onClick={handleResendEmail}
              disabled={isResending}
              className="text-blue-600 dark:text-blue-400 hover:underline disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isResending ? 'Sending...' : 'Resend confirmation email'}
            </button>
          ) : (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Resend email in {countdown} seconds
            </p>
          )}
        </div>

        {/* Resend Message */}
        {resendMessage && (
          <div className={`text-center text-sm ${resendMessage.includes('Failed') ? 'text-red-500' : 'text-green-500'}`}>
            {resendMessage}
          </div>
        )}

        {/* Back Button */}
        <button
          onClick={onBack}
          className="w-full py-2 px-4 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
        >
          ← Back to Sign Up
        </button>
      </div>

      {/* Help Text */}
      <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
        <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
          Didn&apos;t receive the email?
        </h4>
        <ul className="text-xs text-yellow-700 dark:text-yellow-300 space-y-1">
          <li>• Check your spam/junk folder</li>
          <li>• Make sure the email address is correct</li>
          <li>• Wait a few minutes for the email to arrive</li>
          <li>• Try resending the confirmation email</li>
        </ul>
      </div>
    </motion.div>
  );
}
