import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { Suspense, memo } from 'react';
import { db } from '@/lib/supabase';
import HielProfileWrapper from '@/components/hiellinks/HielProfileWrapper';

interface HielLinksPageProps {
  params: Promise<{ username: string }>;
}

// Memoized loading component for better performance
const ProfileLoading = memo(function ProfileLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 flex items-center justify-center">
      <div className="text-center space-y-4">
        <div className="relative w-24 h-24 mx-auto">
          <div className="absolute inset-0 rounded-full border-4 border-blue-200 border-t-blue-600 animate-spin"></div>
          <div className="absolute inset-2 rounded-full border-4 border-purple-200 border-t-purple-600 animate-spin animate-reverse"></div>
        </div>
        <div className="space-y-2">
          <h3 className="text-xl font-semibold text-white">Loading HielLinks...</h3>
          <p className="text-blue-200">Please wait while we load the profile...</p>
        </div>
      </div>
    </div>
  );
});

// Error boundary for profile loading
function ProfileError({ username }: { error?: Error; username?: string }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-red-900 via-pink-900 to-purple-900 flex items-center justify-center">
      <div className="text-center space-y-4 max-w-md mx-auto p-6">
        <div className="text-6xl">❌</div>
        <h3 className="text-xl font-semibold text-white">Profile Not Found</h3>
        <p className="text-red-200">
          {username ? (
            `The profile "@${username}" could not be found or is not available.`
          ) : (
            'Unable to load the profile. The user might not exist or there might be a temporary issue.'
          )}
        </p>
        <button 
          onClick={() => window.location.reload()}
          className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    </div>
  );
}

// Memoized wrapper component to prevent unnecessary re-renders
const MemoizedHielProfileWrapper = memo(HielProfileWrapper);

// Generate metadata for SEO with caching
export async function generateMetadata({ params }: HielLinksPageProps): Promise<Metadata> {
  try {
    const { username } = await params;
    
    // Validate username format
    if (!username || typeof username !== 'string' || username.length < 2) {
      return {
        title: 'Invalid Profile - HielLinks',
        description: 'The requested profile URL is invalid.',
        robots: 'noindex,nofollow'
      };
    }

    console.log('Generating metadata for username:', username);
    const profile = await db.getHielProfile(username.toLowerCase());

    if (!profile || profile.status !== 'published') {
      return {
        title: 'Profile Not Found - HielLinks',
        description: 'The requested profile could not be found or is not available.',
        robots: 'noindex,nofollow'
      };
    }

    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://hieltech.com';
    const profileUrl = `${baseUrl}/hiellinks/${username}`;

    return {
      title: `${profile.business_name} (@${profile.username}) - HielLinks`,
      description: profile.description || `Connect with ${profile.business_name}. Find all their important links and contact information in one place.`,
      keywords: [
        profile.business_name,
        profile.username,
        'links',
        'contact',
        'business',
        'hiellinks',
        ...(profile.description?.split(' ').slice(0, 5) || [])
      ],
      authors: [{ name: profile.business_name }],
      creator: profile.business_name,
      publisher: 'HielTech',
      formatDetection: {
        email: false,
        address: false,
        telephone: false,
      },
      openGraph: {
        title: `${profile.business_name} - HielLinks`,
        description: profile.description || `Connect with ${profile.business_name}. Find all their important links and contact information.`,
        url: profileUrl,
        siteName: 'HielLinks',
        images: profile.logo_url ? [{
          url: profile.logo_url,
          width: 800,
          height: 600,
          alt: `${profile.business_name} logo`,
        }] : [{
          url: `${baseUrl}/hiellinks-og.jpg`,
          width: 1200,
          height: 630,
          alt: 'HielLinks Profile',
        }],
        locale: 'en_US',
        type: 'profile',
      },
      twitter: {
        card: 'summary_large_image',
        title: `${profile.business_name} - HielLinks`,
        description: profile.description || `Connect with ${profile.business_name}`,
        images: profile.logo_url ? [profile.logo_url] : [`${baseUrl}/hiellinks-og.jpg`],
        creator: '@hieltech',
      },
      alternates: {
        canonical: profileUrl,
      },
      other: {
        'profile:username': profile.username,
        'business:contact_data:country_name': profile.location || '',
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'HielLinks Profile',
      description: 'Business profile and links',
      robots: 'noindex,nofollow'
    };
  }
}

// Optimized page component with error handling
export default async function HielLinksProfilePage({ params }: HielLinksPageProps) {
  try {
    const { username } = await params;
    
    console.log('Loading HielLinks page for username:', username);
    
    // Validate username
    if (!username || typeof username !== 'string' || username.length < 2) {
      console.log('Invalid username format:', username);
      notFound();
    }

    // Normalize username (lowercase, trim)
    const normalizedUsername = username.toLowerCase().trim();
    console.log('Normalized username:', normalizedUsername);
    
    // Fetch profile with error handling
    console.log('Fetching profile from database...');
    const profile = await db.getHielProfile(normalizedUsername);
    console.log('Profile fetch result:', profile ? 'Found' : 'Not found');
    
    if (!profile) {
      console.log('Profile not found, returning 404');
      notFound();
    }

    // Check if profile is published
    if (profile.status !== 'published') {
      console.log('Profile not published, status:', profile.status);
      notFound();
    }

    console.log('Profile loaded successfully, rendering component');

    // Use a key based on profile id to prevent unnecessary re-renders
    return (
      <div key={`profile-${profile.id}`}>
        <Suspense fallback={<ProfileLoading />}>
          <MemoizedHielProfileWrapper profile={profile} />
        </Suspense>
      </div>
    );
  } catch (error) {
    console.error('Error loading HielLinks profile:', error);
    const { username } = await params;
    return <ProfileError username={username} />;
  }
}

// Export for static optimization
export const dynamic = 'force-dynamic';
export const revalidate = 300; // Revalidate every 5 minutes
