'use client';

import { HielProfile } from '@/lib/supabase';
import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect, useCallback, useMemo, memo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import dynamic from 'next/dynamic';

// Lazy load QR Code Modal for better performance
const QRCodeModal = dynamic(() => import('./QRCodeModal'), {
  ssr: false,
  loading: () => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg">
        <div className="animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto"></div>
      </div>
    </div>
  )
});

interface HielPublicProfileProps {
  profile: HielProfile;
  onLinkClick: (linkId: string, url: string) => void;
}

// Performance optimizations and memoized components
const MemoizedBackground = motion.div;
const MemoizedFloatingElements = motion.div;

function HielPublicProfile({ profile, onLinkClick }: HielPublicProfileProps) {
  const [imageError, setImageError] = useState(false);
  const [showMap, setShowMap] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');
  const [shareSupported, setShareSupported] = useState(false);

  // Memoized computations
  const activeLinks = useMemo(() => 
    profile.links?.filter(link => link.is_active).sort((a, b) => a.sort_order - b.sort_order) || [], 
    [profile.links]
  );

  const themeColorWithOpacity = useMemo(() => profile.theme_color + '20', [profile.theme_color]);
  const themeColorSolid = useMemo(() => profile.theme_color, [profile.theme_color]);

  useEffect(() => {
    setIsLoaded(true);
    setCurrentUrl(window.location.href);
    setShareSupported(!!navigator.share);

    // Preload images for better UX
    if (profile.logo_url) {
      const img = new window.Image();
      img.src = profile.logo_url;
    }
  }, [profile.logo_url]);

  const getPlatformIcon = useCallback((platform?: string) => {
    const icons: Record<string, string> = {
      instagram: '📷',
      facebook: '📘',
      twitter: '🐦',
      linkedin: '💼',
      youtube: '📺',
      tiktok: '🎵',
      whatsapp: '💬',
      telegram: '✈️',
      github: '🐙',
      website: '🌐',
      email: '✉️',
      phone: '📞'
    };
    return icons[platform || ''] || '🔗';
  }, []);

  const formatUrl = useCallback((url: string) => {
    return url.startsWith('http') ? url : `https://${url}`;
  }, []);

  const handleShare = useCallback(async () => {
    const url = currentUrl || window.location.href;
    try {
      if (shareSupported && navigator.share) {
        await navigator.share({
          title: profile.business_name,
          text: profile.description || `Check out ${profile.business_name}'s links`,
          url: url,
        });
      } else if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(url);
        // Show a toast notification
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg z-50 animate-bounce';
        toast.textContent = 'Link copied to clipboard!';
        document.body.appendChild(toast);
        setTimeout(() => document.body.removeChild(toast), 3000);
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = url;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('Link copied to clipboard!');
      }
    } catch (error) {
      console.error('Share/copy failed:', error);
      // Final fallback - just show the URL to copy manually
      prompt('Copy this link:', url);
    }
  }, [currentUrl, shareSupported, profile.business_name, profile.description]);

  const toggleMap = useCallback(() => setShowMap(prev => !prev), []);

  // Enhanced animations with better performance
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        duration: 0.6,
        when: "beforeChildren" as const,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { duration: 0.4 }
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Dynamic Background with better performance */}
      <MemoizedBackground 
        className="absolute inset-0 bg-gradient-to-br opacity-10"
        style={{ 
          background: `linear-gradient(135deg, ${themeColorSolid}20 0%, ${themeColorSolid}05 50%, transparent 100%)`
        }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.1 }}
        transition={{ duration: 1 }}
      />
      
      {/* Optimized Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
                      <MemoizedFloatingElements
              key={i}
              className="absolute w-2 h-2 rounded-full opacity-20"
              style={{ 
                backgroundColor: themeColorSolid,
                left: `${20 + i * 15}%`,
                top: `${30 + i * 10}%`,
              }}
              animate={{
                y: [0, -20, 0],
                x: [0, 10, 0],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 3 + i,
                repeat: Infinity,
                delay: i * 0.5,
              }}
            />
        ))}
      </div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={isLoaded ? "visible" : "hidden"}
        className="relative z-10 min-h-screen flex items-center justify-center p-4"
      >
        <div className="w-full max-w-md mx-auto">
          {/* Main Profile Card with enhanced performance */}
          <motion.div
            variants={itemVariants}
            className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/50 overflow-hidden"
            whileHover={{ y: -2, scale: 1.01 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            {/* Header with Background */}
            <motion.div
              variants={itemVariants}
              className="relative h-40 overflow-hidden"
            >
              {profile.background_image_url ? (
                <div className="relative w-full h-full">
                  <Image
                    src={profile.background_image_url}
                    alt={`${profile.business_name} background`}
                    fill
                    className="object-cover"
                    priority
                    placeholder="blur"
                    blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                  />
                </div>
              ) : (
                <div 
                  className="absolute inset-0 bg-gradient-to-br"
                  style={{ 
                    background: `linear-gradient(135deg, ${themeColorSolid} 0%, ${themeColorSolid}80 100%)`
                  }}
                />
              )}
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
              
              {/* Profile Picture - Floating with lazy loading */}
              <motion.div
                variants={itemVariants}
                className="absolute -bottom-12 left-1/2 transform -translate-x-1/2"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div 
                  className="w-24 h-24 rounded-full border-4 border-white shadow-2xl overflow-hidden bg-white"
                  style={{ borderColor: themeColorSolid }}
                >
                  {profile.logo_url && !imageError ? (
                    <Image
                      src={profile.logo_url}
                      alt={profile.business_name}
                      width={96}
                      height={96}
                      className="object-cover w-full h-full"
                      onError={() => setImageError(true)}
                      loading="lazy"
                    />
                  ) : (
                    <div 
                      className="w-full h-full flex items-center justify-center text-2xl font-bold text-white"
                      style={{ backgroundColor: themeColorSolid }}
                    >
                      {profile.business_name.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
              </motion.div>
            </motion.div>

            {/* Content */}
            <div className="pt-16 pb-8 px-6">
              {/* Business Info */}
              <motion.div 
                variants={itemVariants}
                className="text-center mb-6"
              >
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  {profile.business_name}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  @{profile.username}
                </p>
                {profile.description && (
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {profile.description}
                  </p>
                )}
              </motion.div>

              {/* Contact Info Grid with enhanced performance */}
              <motion.div 
                variants={itemVariants}
                className="grid grid-cols-1 gap-3 mb-6"
              >
                {profile.location && (
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="group flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/50 rounded-2xl cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300"
                    onClick={toggleMap}
                  >
                    <div className="flex items-center space-x-3">
                      <div 
                        className="w-10 h-10 rounded-xl flex items-center justify-center"
                        style={{ backgroundColor: themeColorWithOpacity }}
                      >
                        <span className="text-lg">📍</span>
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 font-medium">
                        {profile.location}
                      </span>
                    </div>
                    <motion.div
                      animate={{ rotate: showMap ? 180 : 0 }}
                      className="text-gray-400"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </motion.div>
                  </motion.div>
                )}

                {profile.email && (
                  <motion.a
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    href={`mailto:${profile.email}`}
                    className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-2xl hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 group"
                  >
                    <div 
                      className="w-10 h-10 rounded-xl flex items-center justify-center"
                      style={{ backgroundColor: themeColorWithOpacity }}
                    >
                      <span className="text-lg">✉️</span>
                    </div>
                    <span className="text-gray-700 dark:text-gray-300 font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {profile.email}
                    </span>
                  </motion.a>
                )}

                {profile.phone && (
                  <motion.a
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    href={`tel:${profile.phone}`}
                    className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-2xl hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 group"
                  >
                    <div 
                      className="w-10 h-10 rounded-xl flex items-center justify-center"
                      style={{ backgroundColor: themeColorWithOpacity }}
                    >
                      <span className="text-lg">📞</span>
                    </div>
                    <span className="text-gray-700 dark:text-gray-300 font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {profile.phone}
                    </span>
                  </motion.a>
                )}
              </motion.div>

              {/* Map Section with lazy loading */}
              <AnimatePresence>
                {showMap && profile.location && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 200, opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="mb-6 rounded-2xl overflow-hidden border border-gray-200 dark:border-gray-700"
                  >
                    <iframe
                      src={`https://maps.google.com/maps?q=${encodeURIComponent(profile.location)}&output=embed`}
                      width="100%"
                      height="200"
                      style={{ border: 0 }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      className="w-full h-full"
                    />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Website Button */}
              {profile.website && (
                <motion.div 
                  variants={itemVariants}
                  className="mb-6"
                >
                  <motion.a
                    href={formatUrl(profile.website)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full flex items-center justify-center space-x-2 py-4 rounded-2xl text-white font-semibold shadow-lg transition-all duration-300"
                    style={{ backgroundColor: themeColorSolid }}
                    whileHover={{ scale: 1.02, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span className="text-xl">🌐</span>
                    <span>Visit Website</span>
                  </motion.a>
                </motion.div>
              )}

              {/* Links Section with enhanced performance */}
              <motion.div 
                variants={itemVariants}
                className="space-y-3"
              >
                {activeLinks.map((link, index) => (
                  <motion.button
                    key={link.id}
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.9 + index * 0.1 }}
                    onClick={() => onLinkClick(link.id, formatUrl(link.url))}
                    className="w-full p-4 bg-white dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 hover:shadow-xl transition-all duration-300 group"
                    whileHover={{ scale: 1.02, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center space-x-4">
                      <div 
                        className="w-12 h-12 rounded-xl flex items-center justify-center text-xl shadow-md"
                        style={{ backgroundColor: themeColorWithOpacity }}
                      >
                        {getPlatformIcon(link.platform)}
                      </div>
                      <div className="flex-1 text-left">
                        <div className="font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                          {link.title}
                        </div>
                        {link.description && (
                          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                            {link.description}
                          </div>
                        )}
                      </div>
                      <motion.div
                        className="text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"
                        whileHover={{ x: 5 }}
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </motion.div>
                    </div>
                  </motion.button>
                ))}
              </motion.div>

              {activeLinks.length === 0 && (
                <motion.div 
                  variants={itemVariants}
                  className="text-center py-12"
                >
                  <div className="text-4xl mb-4">🔗</div>
                  <p className="text-gray-600 dark:text-gray-400">
                    No links available yet.
                  </p>
                </motion.div>
              )}

              {/* Footer */}
              <motion.div
                variants={itemVariants}
                className="text-center mt-8 pt-6 border-t border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-center justify-center space-x-2 text-gray-500 dark:text-gray-400 text-sm">
                  <span>Powered by</span>
                  <Link
                    href="/"
                    className="font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                  >
                    HielTech
                  </Link>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Floating Action Buttons with better accessibility */}
          <motion.div
            variants={itemVariants}
            className="flex justify-center space-x-4 mt-6"
          >
            {/* Share Button */}
            <motion.button
              onClick={handleShare}
              className="w-14 h-14 rounded-full shadow-lg flex items-center justify-center text-white transition-all duration-300 backdrop-blur-sm focus:outline-none focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-800"
              style={{ backgroundColor: themeColorSolid + 'CC' }}
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.9 }}
              aria-label="Share profile"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
            </motion.button>

            {/* QR Code Button */}
            <motion.button
              onClick={() => setShowQRCode(true)}
              className="w-14 h-14 rounded-full bg-white/20 dark:bg-gray-800/50 backdrop-blur-sm shadow-lg flex items-center justify-center text-gray-700 dark:text-gray-300 transition-all duration-300 border border-white/30 focus:outline-none focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600"
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.9 }}
              aria-label="Show QR code"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
              </svg>
            </motion.button>
          </motion.div>
        </div>
      </motion.div>

      {/* QR Code Modal with lazy loading */}
      {currentUrl && (
        <QRCodeModal
          isOpen={showQRCode}
          onClose={() => setShowQRCode(false)}
          url={currentUrl}
          title={profile.business_name}
          themeColor={themeColorSolid}
        />
      )}
    </div>
  );
}

// Export memoized component to prevent unnecessary re-renders
export default memo(HielPublicProfile);
