'use client';

import { HielLink } from '@/lib/supabase';

interface HielLinkEditorProps {
  link: Partial<HielLink>;
  onChange: (linkData: Partial<HielLink>) => void;
  onRemove: () => void;
}

const PLATFORM_OPTIONS = [
  { value: 'instagram', label: 'Instagram', icon: '📷' },
  { value: 'facebook', label: 'Facebook', icon: '📘' },
  { value: 'twitter', label: 'Twitter/X', icon: '🐦' },
  { value: 'linkedin', label: 'LinkedIn', icon: '💼' },
  { value: 'youtube', label: 'YouTube', icon: '📺' },
  { value: 'tiktok', label: 'TikTok', icon: '🎵' },
  { value: 'whatsapp', label: 'WhatsApp', icon: '💬' },
  { value: 'telegram', label: 'Telegram', icon: '✈️' },
  { value: 'github', label: 'GitHub', icon: '🐙' },
  { value: 'other', label: 'Other', icon: '🔗' },
];

const LINK_TYPES = [
  { value: 'website', label: 'Website' },
  { value: 'social', label: 'Social Media' },
  { value: 'contact', label: 'Contact' },
  { value: 'custom', label: 'Custom' },
];

export default function HielLinkEditor({ link, onChange, onRemove }: HielLinkEditorProps) {
  const handleChange = (field: keyof HielLink, value: string | boolean) => {
    onChange({ ...link, [field]: value });
  };

  const getPlatformIcon = (platform?: string) => {
    const platformOption = PLATFORM_OPTIONS.find(p => p.value === platform);
    return platformOption?.icon || '🔗';
  };

  const validateUrl = (url: string) => {
    if (!url) return true;
    try {
      new URL(url.startsWith('http') ? url : `https://${url}`);
      return true;
    } catch {
      return false;
    }
  };

  const formatUrl = (url: string) => {
    if (!url) return '';
    return url.startsWith('http') ? url : `https://${url}`;
  };

  return (
    <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getPlatformIcon(link.platform)}</span>
          <span className="font-medium text-gray-900 dark:text-white">
            Link {link.type === 'social' && link.platform ? `- ${PLATFORM_OPTIONS.find(p => p.value === link.platform)?.label}` : ''}
          </span>
        </div>
        <button
          type="button"
          onClick={onRemove}
          className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 text-sm"
        >
          Remove
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Title *
          </label>
          <input
            type="text"
            value={link.title || ''}
            onChange={(e) => handleChange('title', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white text-sm"
            placeholder="Link title"
            required
          />
        </div>

        {/* URL */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            URL *
          </label>
          <input
            type="url"
            value={link.url || ''}
            onChange={(e) => handleChange('url', formatUrl(e.target.value))}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white text-sm ${
              link.url && !validateUrl(link.url)
                ? 'border-red-300 dark:border-red-600'
                : 'border-gray-300 dark:border-gray-600'
            }`}
            placeholder="https://example.com"
            required
          />
          {link.url && !validateUrl(link.url) && (
            <p className="mt-1 text-xs text-red-600 dark:text-red-400">
              Please enter a valid URL
            </p>
          )}
        </div>

        {/* Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Type
          </label>
          <select
            value={link.type || 'custom'}
            onChange={(e) => handleChange('type', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white text-sm"
          >
            {LINK_TYPES.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Platform (only for social media) */}
        {link.type === 'social' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Platform
            </label>
            <select
              value={link.platform || 'other'}
              onChange={(e) => handleChange('platform', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white text-sm"
            >
              {PLATFORM_OPTIONS.map((platform) => (
                <option key={platform.value} value={platform.value}>
                  {platform.icon} {platform.label}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Description */}
      <div className="mt-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Description (Optional)
        </label>
        <input
          type="text"
          value={link.description || ''}
          onChange={(e) => handleChange('description', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white text-sm"
          placeholder="Brief description of this link"
        />
      </div>

      {/* Active Toggle */}
      <div className="mt-4 flex items-center">
        <input
          type="checkbox"
          id={`active-${link.title}`}
          checked={link.is_active !== false}
          onChange={(e) => handleChange('is_active', e.target.checked)}
          className="mr-2"
        />
        <label htmlFor={`active-${link.title}`} className="text-sm text-gray-700 dark:text-gray-300">
          Active (visible on profile)
        </label>
      </div>

      {/* Preview */}
      {link.title && link.url && (
        <div className="mt-4 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md">
          <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Preview:</p>
          <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
            <span className="text-lg">{getPlatformIcon(link.platform)}</span>
            <div className="flex-1">
              <div className="font-medium text-gray-900 dark:text-white text-sm">
                {link.title}
              </div>
              {link.description && (
                <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  {link.description}
                </div>
              )}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {link.is_active !== false ? '✓ Active' : '✗ Inactive'}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
