'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { BlogPost, BlogPostAnalytics, supabase } from '@/lib/supabase';
import { motion } from 'framer-motion';

interface BlogAnalyticsProps {
  posts: BlogPost[];
}

export default function BlogAnalytics({ posts }: BlogAnalyticsProps) {
  const [analytics, setAnalytics] = useState<BlogPostAnalytics[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [selectedMetric, setSelectedMetric] = useState<'views' | 'likes' | 'comments'>('views');

  const loadAnalytics = useCallback(async () => {
    setLoading(true);
    try {
      const endDate = new Date();
      const startDate = new Date();

      switch (timeRange) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      const { data, error } = await supabase
        .from('blog_post_analytics')
        .select('*')
        .gte('date', startDate.toISOString().split('T')[0])
        .lte('date', endDate.toISOString().split('T')[0])
        .order('date', { ascending: true });

      if (error) throw error;
      setAnalytics(data || []);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  useEffect(() => {
    loadAnalytics();
  }, [loadAnalytics]);

  const totalPosts = posts.length;
  const publishedPosts = posts.filter(p => p.status === 'published').length;
  const draftPosts = posts.filter(p => p.status === 'draft').length;
  const scheduledPosts = posts.filter(p => p.status === 'scheduled').length;
  const featuredPosts = posts.filter(p => p.is_featured).length;

  const totalViews = posts.reduce((sum, post) => sum + post.view_count, 0);
  const totalLikes = posts.reduce((sum, post) => sum + post.like_count, 0);
  const totalComments = posts.reduce((sum, post) => sum + post.comment_count, 0);

  const avgReadingTime = posts.length > 0
    ? Math.round(posts.reduce((sum, post) => sum + post.reading_time, 0) / posts.length)
    : 0;

  // Calculate analytics metrics
  const analyticsUniqueViews = analytics.reduce((sum, a) => sum + a.unique_views, 0);

  // Calculate growth rates
  const midPoint = Math.floor(analytics.length / 2);
  const firstHalf = analytics.slice(0, midPoint);
  const secondHalf = analytics.slice(midPoint);

  const firstHalfViews = firstHalf.reduce((sum, a) => sum + a.views, 0);
  const secondHalfViews = secondHalf.reduce((sum, a) => sum + a.views, 0);
  const viewsGrowth = firstHalfViews > 0 ? ((secondHalfViews - firstHalfViews) / firstHalfViews) * 100 : 0;

  const topPosts = [...posts]
    .filter(p => p.status === 'published')
    .sort((a, b) => b.view_count - a.view_count)
    .slice(0, 5);

  const recentPosts = [...posts]
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 5);

  const stats = [
    {
      label: 'Total Posts',
      value: totalPosts,
      icon: '📝',
      color: 'blue',
      change: null
    },
    {
      label: 'Published',
      value: publishedPosts,
      icon: '✅',
      color: 'green',
      change: null
    },
    {
      label: 'Total Views',
      value: totalViews.toLocaleString(),
      icon: '👁️',
      color: 'indigo',
      change: viewsGrowth > 0 ? `+${viewsGrowth.toFixed(1)}%` : viewsGrowth < 0 ? `${viewsGrowth.toFixed(1)}%` : null
    },
    {
      label: 'Unique Views',
      value: analyticsUniqueViews.toLocaleString(),
      icon: '👥',
      color: 'purple',
      change: null
    },
    {
      label: 'Total Likes',
      value: totalLikes.toLocaleString(),
      icon: '❤️',
      color: 'red',
      change: null
    },
    {
      label: 'Comments',
      value: totalComments.toLocaleString(),
      icon: '💬',
      color: 'yellow',
      change: null
    },
    {
      label: 'Avg. Reading Time',
      value: `${avgReadingTime} min`,
      icon: '⏱️',
      color: 'teal',
      change: null
    },
    {
      label: 'Featured Posts',
      value: featuredPosts,
      icon: '⭐',
      color: 'orange',
      change: null
    },
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800',
      green: 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 border-green-200 dark:border-green-800',
      gray: 'bg-gray-50 dark:bg-gray-900/20 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-800',
      yellow: 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400 border-yellow-200 dark:border-yellow-800',
      purple: 'bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-800',
      indigo: 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 border-indigo-200 dark:border-indigo-800',
      red: 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 border-red-200 dark:border-red-800',
      teal: 'bg-teal-50 dark:bg-teal-900/20 text-teal-600 dark:text-teal-400 border-teal-200 dark:border-teal-800',
      orange: 'bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 border-orange-200 dark:border-orange-800',
    };
    return colors[color as keyof typeof colors] || colors.gray;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  // Prepare chart data
  const chartData = analytics.map(item => ({
    date: formatDate(item.date),
    views: item.views,
    unique_views: item.unique_views,
    likes: item.likes,
    comments: item.comments
  }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Blog Analytics Dashboard
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Track your blog performance and engagement metrics
          </p>
        </div>

        {/* Time Range Selector */}
        <div className="mt-4 sm:mt-0">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as '7d' | '30d' | '90d' | '1y')}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading analytics...</span>
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`p-4 rounded-lg border ${getColorClasses(stat.color)}`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium opacity-75">{stat.label}</p>
                <p className="text-2xl font-bold">{stat.value}</p>
                {stat.change && (
                  <p className={`text-xs font-medium mt-1 ${
                    stat.change.startsWith('+') ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {stat.change} vs previous period
                  </p>
                )}
              </div>
              <span className="text-2xl opacity-75">{stat.icon}</span>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Section */}
      {!loading && analytics.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Views Chart */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-md font-semibold text-gray-900 dark:text-white">
                Views Over Time
              </h4>
              <div className="flex space-x-2">
                <button
                  onClick={() => setSelectedMetric('views')}
                  className={`px-3 py-1 text-xs rounded-full ${
                    selectedMetric === 'views'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                  }`}
                >
                  Views
                </button>
                <button
                  onClick={() => setSelectedMetric('likes')}
                  className={`px-3 py-1 text-xs rounded-full ${
                    selectedMetric === 'likes'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                  }`}
                >
                  Likes
                </button>
                <button
                  onClick={() => setSelectedMetric('comments')}
                  className={`px-3 py-1 text-xs rounded-full ${
                    selectedMetric === 'comments'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                  }`}
                >
                  Comments
                </button>
              </div>
            </div>

            {/* Simple Chart Visualization */}
            <div className="h-48 flex items-end space-x-1">
              {chartData.slice(-14).map((item, index) => {
                const maxValue = Math.max(...chartData.map(d => d[selectedMetric]));
                const height = maxValue > 0 ? (item[selectedMetric] / maxValue) * 100 : 0;

                return (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div
                      className="w-full bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-600"
                      style={{ height: `${height}%`, minHeight: height > 0 ? '4px' : '0px' }}
                      title={`${item.date}: ${item[selectedMetric]} ${selectedMetric}`}
                    />
                    <span className="text-xs text-gray-500 dark:text-gray-400 mt-1 transform -rotate-45 origin-left">
                      {item.date}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Engagement Metrics */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-4">
              Engagement Metrics
            </h4>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Avg. Views per Post</span>
                <span className="font-medium">
                  {publishedPosts > 0 ? Math.round(totalViews / publishedPosts) : 0}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Engagement Rate</span>
                <span className="font-medium">
                  {totalViews > 0 ? ((totalLikes + totalComments) / totalViews * 100).toFixed(1) : 0}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Comments per Post</span>
                <span className="font-medium">
                  {publishedPosts > 0 ? (totalComments / publishedPosts).toFixed(1) : 0}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Likes per Post</span>
                <span className="font-medium">
                  {publishedPosts > 0 ? (totalLikes / publishedPosts).toFixed(1) : 0}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Posts */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-4">
            Top Performing Posts
          </h4>
          <div className="space-y-3">
            {topPosts.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                No published posts yet
              </p>
            ) : (
              topPosts.map((post, index) => (
                <div key={post.id} className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {index + 1}. {post.title}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {post.category?.name} • {formatDate(post.created_at)}
                    </p>
                  </div>
                  <div className="text-right ml-4">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {post.view_count.toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">views</p>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Recent Posts */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-4">
            Recent Posts
          </h4>
          <div className="space-y-3">
            {recentPosts.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                No posts created yet
              </p>
            ) : (
              recentPosts.map((post) => (
                <div key={post.id} className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {post.title}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {post.category?.name} • {formatDate(post.created_at)}
                    </p>
                  </div>
                  <div className="text-right ml-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      post.status === 'published' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                        : post.status === 'draft'
                        ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                        : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                    }`}>
                      {post.status}
                    </span>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Content Distribution */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-4">
          Content Distribution
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {publishedPosts}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Published</div>
            <div className="text-xs text-gray-500 dark:text-gray-500">
              {totalPosts > 0 ? Math.round((publishedPosts / totalPosts) * 100) : 0}%
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600 dark:text-gray-400">
              {draftPosts}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Drafts</div>
            <div className="text-xs text-gray-500 dark:text-gray-500">
              {totalPosts > 0 ? Math.round((draftPosts / totalPosts) * 100) : 0}%
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {scheduledPosts}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Scheduled</div>
            <div className="text-xs text-gray-500 dark:text-gray-500">
              {totalPosts > 0 ? Math.round((scheduledPosts / totalPosts) * 100) : 0}%
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {featuredPosts}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Featured</div>
            <div className="text-xs text-gray-500 dark:text-gray-500">
              {totalPosts > 0 ? Math.round((featuredPosts / totalPosts) * 100) : 0}%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
