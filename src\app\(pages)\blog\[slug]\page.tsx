import { notFound } from 'next/navigation';
import { db } from '@/lib/supabase';
import { Metadata } from 'next';
import BlogPostTemplate from '@/components/blog/BlogPostTemplate';

interface BlogPostPageProps {
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params;
  const post = await db.getBlogPostBySlug(slug);
  
  if (!post || post.status !== 'published') {
    return {
      title: 'Post Not Found',
    };
  }

  return {
    title: post.meta_title || post.title,
    description: post.meta_description || post.excerpt || 'Read this blog post on Hiel Tech',
    keywords: post.meta_keywords,
    openGraph: {
      title: post.og_title || post.title,
      description: post.og_description || post.excerpt || 'Read this blog post on Hiel Tech',
      images: post.og_image ? [post.og_image.file_path] : post.featured_image ? [post.featured_image.file_path] : [],
      type: 'article',
      publishedTime: post.published_at || post.created_at,
      authors: [post.author?.display_name || 'Hiel Tech'],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.og_title || post.title,
      description: post.og_description || post.excerpt || 'Read this blog post on Hiel Tech',
      images: post.og_image ? [post.og_image.file_path] : post.featured_image ? [post.featured_image.file_path] : [],
    },
  };
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const post = await db.getBlogPostBySlug(slug);

  if (!post || post.status !== 'published') {
    notFound();
  }

  // Increment view count
  await db.incrementPostViewCount(post.id);

  return (
    <div className="min-h-screen">
      <section className="relative py-16 md:py-24 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <BlogPostTemplate post={post} />
        </div>
      </section>
    </div>
  );
}
